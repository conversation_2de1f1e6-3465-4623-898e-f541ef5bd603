"""
Server-Sent Events (SSE) routes for chat streaming.
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.api.dependencies import get_current_user
from app.services.chat_service import chat_service

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/sse", tags=["sse"])

class ChatStreamRequest(BaseModel):
    """Request model for chat streaming."""
    message: str
    session_id: str
    use_agent: bool = False

@router.post("/chat/stream")
async def stream_chat_response(
    request: ChatStreamRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Stream chat response using Server-Sent Events (SSE).
    
    This endpoint provides real-time streaming of chat responses
    with better performance than WebSocket word-by-word delivery.
    """
    try:
        user_id = current_user["id"]
        
        async def generate_stream():
            try:
                # Send initial connection event
                yield f"data: {json.dumps({'type': 'connected', 'message': 'Stream connected'})}\n\n"
                
                # Get the chat response
                response = await chat_service.send_message(
                    session_id=request.session_id,
                    user_id=user_id,
                    message=request.message,
                    use_agent=request.use_agent
                )
                
                if response.get("error"):
                    # Send error event
                    error_data = {
                        "type": "error",
                        "error": response["error"],
                        "message": "An error occurred while processing your request"
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"
                    return
                
                # Stream the response in larger, more efficient chunks
                response_text = response.get("response", "")
                
                if response_text:
                    # Split into sentences for natural streaming
                    sentences = response_text.split('. ')
                    
                    for i, sentence in enumerate(sentences):
                        if sentence.strip():
                            is_final = (i == len(sentences) - 1)
                            
                            # Prepare sentence chunk
                            chunk = sentence.strip()
                            if not is_final and not chunk.endswith('.'):
                                chunk += ". "
                            
                            # Send chunk event
                            chunk_data = {
                                "type": "chunk",
                                "chunk": chunk,
                                "is_final": is_final,
                                "metadata": {
                                    "sources": response.get("sources", []),
                                    "chart_data": response.get("chart_data")
                                }
                            }
                            yield f"data: {json.dumps(chunk_data)}\n\n"
                            
                            # Small delay for natural reading pace
                            await asyncio.sleep(0.3)
                
                # Send completion event
                completion_data = {
                    "type": "complete",
                    "message": "Response complete",
                    "metadata": {
                        "sources": response.get("sources", []),
                        "chart_data": response.get("chart_data"),
                        "session_id": request.session_id
                    }
                }
                yield f"data: {json.dumps(completion_data)}\n\n"
                
            except Exception as e:
                logger.error(f"Error in chat stream: {str(e)}")
                error_data = {
                    "type": "error",
                    "error": str(e),
                    "message": "An unexpected error occurred"
                }
                yield f"data: {json.dumps(error_data)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"Error setting up chat stream: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error setting up chat stream: {str(e)}"
        )

@router.get("/health")
async def sse_health_check():
    """Health check for SSE endpoint."""
    return {"status": "ok", "service": "sse"}
