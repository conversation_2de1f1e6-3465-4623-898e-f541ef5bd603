<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AnyDocAI - SSE Chat Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            font-size: 14px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .streaming {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>AnyDocAI - Server-Sent Events Chat</h1>
    <p>This example shows how to use the new SSE endpoint for chat streaming instead of WebSocket.</p>
    
    <div class="chat-container" id="chatContainer">
        <!-- Messages will appear here -->
    </div>
    
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="Type your message here..." />
        <button id="sendButton" onclick="sendMessage()">Send</button>
        <button id="agentToggle" onclick="toggleAgent()">RAG Mode</button>
    </div>
    
    <div class="status" id="status">Ready to chat</div>
    
    <script>
        let isAgentMode = false;
        let isStreaming = false;
        const sessionId = 'demo-session-' + Date.now();
        
        // Mock authentication - replace with real auth
        const mockUser = {
            id: 'demo-user-123',
            token: 'demo-token'
        };
        
        function toggleAgent() {
            isAgentMode = !isAgentMode;
            const button = document.getElementById('agentToggle');
            button.textContent = isAgentMode ? 'Agent Mode' : 'RAG Mode';
            button.style.backgroundColor = isAgentMode ? '#28a745' : '#007bff';
            updateStatus(`Switched to ${isAgentMode ? 'Agent' : 'RAG'} mode`);
        }
        
        function updateStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function addMessage(content, isUser = false) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageDiv;
        }
        
        function sendMessage() {
            if (isStreaming) {
                updateStatus('Please wait for the current response to complete', 'error');
                return;
            }
            
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                updateStatus('Please enter a message', 'error');
                return;
            }
            
            // Add user message to chat
            addMessage(message, true);
            input.value = '';
            
            // Disable send button during streaming
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            isStreaming = true;
            
            // Create assistant message container
            const assistantMessage = addMessage('', false);
            let fullResponse = '';
            
            updateStatus('Connecting to chat stream...', 'streaming');
            
            // Create SSE connection
            const eventSource = new EventSource(`http://localhost:8000/api/sse/chat/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${mockUser.token}`
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    use_agent: isAgentMode
                })
            });
            
            // Note: EventSource doesn't support POST directly
            // In a real implementation, you'd need to:
            // 1. First make a POST request to start the stream
            // 2. Then connect to a GET SSE endpoint with a stream ID
            
            // For this demo, let's simulate the SSE response
            simulateSSEResponse(message, assistantMessage, sendButton);
        }
        
        // Simulate SSE response for demo purposes
        function simulateSSEResponse(userMessage, assistantMessageDiv, sendButton) {
            const responses = [
                "I understand you're asking about: " + userMessage,
                "Let me process this information for you.",
                "Based on the documents available, here's what I found:",
                "This is a simulated response using the new SSE streaming.",
                "The actual implementation will connect to /api/sse/chat/stream endpoint.",
                "This provides much better performance than the previous WebSocket word-by-word streaming."
            ];
            
            let currentResponse = '';
            let sentenceIndex = 0;
            
            const streamInterval = setInterval(() => {
                if (sentenceIndex < responses.length) {
                    const sentence = responses[sentenceIndex];
                    currentResponse += (currentResponse ? ' ' : '') + sentence;
                    assistantMessageDiv.textContent = currentResponse;
                    
                    updateStatus(`Streaming response... (${sentenceIndex + 1}/${responses.length})`, 'streaming');
                    sentenceIndex++;
                } else {
                    // Stream complete
                    clearInterval(streamInterval);
                    updateStatus('Response complete', 'success');
                    sendButton.disabled = false;
                    isStreaming = false;
                }
            }, 800); // Simulate sentence-by-sentence delivery
        }
        
        // Handle Enter key in input
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isStreaming) {
                sendMessage();
            }
        });
        
        // Add initial message
        addMessage('Welcome! This is a demo of the new SSE chat streaming. The excessive WebSocket streaming has been replaced with efficient Server-Sent Events.', false);
    </script>
</body>
</html>
